{"name": "portfolio-vite-app", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.0", "@mui/material": "^5.14.0", "framer-motion": "^10.13.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "styled-components": "^6.0.3"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "vite": "^5.2.0"}}