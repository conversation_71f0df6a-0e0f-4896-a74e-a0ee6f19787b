# 💼 Portfolio Vite App

## 🚀 Vite React Portfolio Template

This is a modern portfolio web application built with **Vite** and **React**. It has been converted from Create React App to Vite for better performance and faster development experience.

### ✨ Features

- **⚡ Vite**: Lightning-fast development server and build tool
- **⚛️ React 18**: Latest React with modern hooks and features
- **🎨 Modern UI**: Beautiful, responsive design with glassmorphism effects
- **📱 Mobile-First**: Fully responsive across all devices
- **🌙 Dark/Light Theme**: Toggle between themes
- **🎯 Smooth Animations**: Engaging micro-interactions
- **📊 Progress Indicators**: Scroll progress and back-to-top functionality

### �️ Tech Stack

- **Frontend**: React 18, React Router DOM
- **Build Tool**: Vite
- **Styling**: CSS3 with custom properties, Glassmorphism
- **Icons**: Material-UI Icons
- **Animations**: Framer Motion, CSS Animations
- **UI Components**: Material-UI, Styled Components

### 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd Portfolio-Web-App
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Build for production**
   ```bash
   npm run build
   ```

5. **Preview production build**
   ```bash
   npm run preview
   ```

### 📁 Project Structure

```
src/
├── components/           # React components
│   ├── aboutMe.jsx      # About section
│   ├── header.jsx       # Navigation header
│   ├── projects.jsx     # Projects showcase
│   ├── footer.jsx       # Footer section
│   ├── contactMe.jsx    # Contact form
│   └── ...
├── images/              # Static images
├── App.jsx              # Main app component
├── main.jsx             # Entry point
└── index.css            # Global styles
```

### 🎨 Customization

1. **Personal Information**: Update the placeholder text in components:
   - `src/components/aboutMe.jsx` - Replace `[YOUR NAME]` and `[YOUR PROFESSION]`
   - `src/components/footer.jsx` - Update copyright and social links
   - `src/components/codingPlatforms.jsx` - Add your platform profiles

2. **Projects**: Update `src/components/projects.jsx` with your projects

3. **Contact Form**: Update the form action in `src/components/contactMe.jsx`

4. **Styling**: Modify CSS files to match your brand colors and preferences

### 🔧 Migration from Create React App

This project has been successfully migrated from Create React App to Vite:

- ✅ Updated `package.json` with Vite scripts and dependencies
- ✅ Created `vite.config.js` configuration
- ✅ Updated `index.html` for Vite structure
- ✅ Renamed entry point from `index.js` to `main.jsx`
- ✅ Updated all component files to use `.jsx` extension
- ✅ Fixed all import statements
- ✅ Removed personal information and replaced with placeholders

### 📝 Notes

- All personal information has been removed and replaced with placeholders
- Social media links and external URLs have been set to `#` placeholders
- The contact form action has been disabled
- Replace all placeholder content with your own information

### 🤝 Contributing

Feel free to fork this project and customize it for your own portfolio!

### 📄 License

This project is open source and available under the [MIT License](LICENSE).
