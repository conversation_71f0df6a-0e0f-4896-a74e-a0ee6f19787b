import React from 'react';
import './App.css';
import Header from './components/header.jsx';
import AboutMe from './components/aboutMe.jsx';
import Projects from './components/projects.jsx';
import SkillsAndExperiences from './components/skillsandexperiences.jsx';
import CodingPlatforms from './components/codingPlatforms.jsx';
import Footer from './components/footer.jsx';
import ContactMe from './components/contactMe.jsx';
import BackToTop from './components/BackToTop.jsx';
import ScrollProgress from './components/ScrollProgress.jsx';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
function App() {
 
  
  return (
    <Router>
    <div>
      <ScrollProgress />
      <Header />

      <Routes>
        <Route path="/" element={
          <>
            <AboutMe />
            <Projects />
            <SkillsAndExperiences />
            <CodingPlatforms />
          </>
        } />

        <Route path="/contactMe" element={
          <>
            <ContactMe />
          </>
        } />

        <Route path="/Projects" element={
          <>
            <Projects />
          </>
        } />

        <Route path="/Platforms" element={
          <>
            <CodingPlatforms />
          </>
        } />
      </Routes>

      <Footer />
      <BackToTop />
    </div>
    </Router>
  );
}


export default App;